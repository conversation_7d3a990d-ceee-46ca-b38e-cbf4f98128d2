def main():
    print("Hello from stress!")


if __name__ == "__main__":
    import pandas as pd
    import numpy as np
    import matplotlib
    matplotlib.use('Agg')  # 使用非交互式后端
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    from datetime import datetime, timedelta

    # 生成时间序列数据 - 从当前时间开始，5分钟内每秒一个数据点
    from datetime import datetime
    current_time = datetime.now()
    date_range = pd.date_range(start=current_time, periods=300, freq="s")  # 5分钟 = 300秒

    # Mock TPS数据 (Transactions Per Second)
    tps_data = np.random.randint(10, 100, size=300)

    # Mock QPS数据 (Queries Per Second) - 模拟请求数
    # QPS为35，上下1或2的波动
    qps_data = np.random.randint(33, 38, size=300)  # 33-37范围

    # Mock RT数据 (Response Time) - 响应时间，单位毫秒
    # 基线300ms，上下50ms波动
    rt_data = np.random.normal(300, 25, size=300)  # 平均300ms，标准差25ms
    rt_data = np.clip(rt_data, 250, 350)  # 限制在250-350ms范围内

    # 创建DataFrame
    df = pd.DataFrame({
        "time": date_range,
        "tps": tps_data,
        "qps": qps_data,
        "rt": rt_data
    })

    # 设置图表样式
    plt.style.use('default')
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))

    # 第一个图表：QPS (Queries Per Second)
    ax1.plot(df['time'], df['qps'], color='green', linewidth=2)
    ax1.fill_between(df['time'], df['qps'], alpha=0.3, color='green')

    ax1.set_title('Total Requests per Second', fontsize=14, fontweight='bold')
    ax1.set_ylabel('Requests/s')
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(0, df['qps'].max() * 1.1)

    # 格式化x轴时间显示
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M:%S'))
    ax1.xaxis.set_major_locator(mdates.SecondLocator(interval=60))  # 每分钟一个刻度
    ax1.set_xlim(df['time'].min(), df['time'].max())

    # 第二个图表：RT (Response Time)
    ax2.plot(df['time'], df['rt'], color='blue', linewidth=2)
    ax2.fill_between(df['time'], df['rt'], alpha=0.3, color='blue')

    ax2.set_title('Response Time', fontsize=14, fontweight='bold')
    ax2.set_ylabel('Response Time (ms)')
    ax2.set_xlabel('Time')
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(0, df['rt'].max() * 1.1)

    # 格式化x轴时间显示
    ax2.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M:%S'))
    ax2.xaxis.set_major_locator(mdates.SecondLocator(interval=60))  # 每分钟一个刻度
    ax2.set_xlim(df['time'].min(), df['time'].max())

    # 调整布局
    plt.tight_layout()
    plt.xticks(rotation=45)

    # 保存图表
    plt.savefig('stress_test_charts.png', dpi=300, bbox_inches='tight')
    print("Charts saved as 'stress_test_charts.png'")

    # 打印一些统计信息
    print(f"QPS Statistics:")
    print(f"  Mean: {df['qps'].mean():.2f}, Max: {df['qps'].max()}")
    print(f"\nResponse Time Statistics:")
    print(f"  Mean RT: {df['rt'].mean():.2f}ms")
    print(f"  95th percentile RT: {df['rt'].quantile(0.95):.2f}ms")
    print(f"  99th percentile RT: {df['rt'].quantile(0.99):.2f}ms")
    print(f"  Max RT: {df['rt'].max():.2f}ms")
